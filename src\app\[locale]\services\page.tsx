import { ServicesListClient } from "@/components/features/services/services-list-client";
import { convertToServiceFilters, getGlobalQueryParams } from "@/lib/utils";
import { getServicesAction } from "@/server/actions/services-actions";

interface ServicesPageProps {
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}

export default async function ServicesPage({
  searchParams,
}: ServicesPageProps) {
  // Parse search params using nuqs server-side utilities
  const params = await getGlobalQueryParams(searchParams);

  // Convert to ServiceApiParams format
  const filters = convertToServiceFilters(params);

  // Fetch initial data using Server Action
  const result = await getServicesAction(filters);

  if (!result.success) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Services</h1>
        <div className="text-red-500">
          Error loading services: {result.error}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Services</h1>

      {/* Client component that will handle real-time updates */}
      <ServicesListClient initialData={result.data} initialFilters={filters} />
    </div>
  );
}
