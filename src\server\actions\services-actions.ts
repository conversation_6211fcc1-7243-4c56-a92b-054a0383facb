"use server";

import { GetAllServicesService } from "@/services/services/get-all-services";
import { GetServicesByUserIdService } from "@/services/services/get-services-by-user-id";

// Service instances
const getAllServicesService = new GetAllServicesService();
const getServicesByUserIdService = new GetServicesByUserIdService();

/**
 * Server Action: Get all services with filters
 */
export async function getServicesAction(filters: ServiceFilters = {}) {
  try {
    const result = await getAllServicesService.getServices(filters);
    return {
      success: true,
      data: result.response,
      status: result.status,
    };
  } catch (error) {
    console.error("Error fetching services:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      data: null,
    };
  }
}

/**
 * Server Action: Get services with advanced filters
 */
export async function getServicesWithFiltersAction(filters: ServiceFilters) {
  try {
    const result = await getAllServicesService.getServicesWithFilters(filters);
    return {
      success: true,
      data: result.response,
      status: result.status,
    };
  } catch (error) {
    console.error("Error fetching filtered services:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      data: null,
    };
  }
}

/**
 * Server Action: Get services by user external ID
 */
export async function getServicesByUserIdAction(userExtl_id: string) {
  try {
    const result = await getServicesByUserIdService.getServicesByUserId(userExtl_id);
    return {
      success: true,
      data: result.response,
      status: result.status,
    };
  } catch (error) {
    console.error("Error fetching user services:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      data: null,
    };
  }
}
