import { z } from "zod";
import { BaseApiService } from "../base-service";

// Zod Schemas for validation
const CategorySchema = z.object({
  id: z.number(),
  intl_id: z.string(),
  name: z.string(),
});

const ProviderSchema = z.object({
  id: z.number(),
  extl_id: z.string(),
  note: z.number().nullable(),
  status: z.enum(["active", "inactive", "deleted", "locked"]),
  firstName: z.string(),
  lastName: z.string(),
  imageUrl: z.string().nullable(),
  email: z.string().nullable(),
  unsafe_metadata: z.record(z.any()).nullable(),
});

const PricingSchema = z.object({
  price: z.number(),
  priceEuro: z.number().nullable(),
  iso_curry: z.string(),
  pricingModel: z.enum(["fixed", "hourly", "daily", "custom"]),
});

const ServiceSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string(),
  longDescription: z.string(),
  isPublished: z.boolean(),
  isLiked: z.boolean().nullable(),
  categories: z.array(CategorySchema),
  provider: ProviderSchema,
  img: z.array(z.string()),
  pricing: PricingSchema,
  locations: z.array(z.string()),
  createdAt: z.string().nullable(),
  updatedAt: z.string().nullable(),
  status: z.enum(["active", "inactive", "deleted", "pending"]),
  rejectionReason: z.string().nullable(),
});

const ServicesListSchema = z.object({
  nb_items: z.number(),
  next_page: z.number().nullable(),
  pagination: z.number(),
  items: z.array(ServiceSchema),
});

export class GetAllServicesService extends BaseApiService {
  private BASE_URL = "services/";

  constructor() {
    super();
  }

  /**
   * Get all services with optional filters
   */
  async getServices(filters: ServiceFilters = {}) {
    const { limit = 12, next = 1, ...otherFilters } = filters;

    return await this.request(
      `${this.BASE_URL}all/${limit}/${next}`,
      {
        method: "GET",
        query: otherFilters,
        tag: `services-${limit}-${next}`,
      },
      ServicesListSchema,
    );
  }

  /**
   * Get services with advanced filters
   */
  async getServicesWithFilters(filters: ServiceFilters) {
    const { limit = 12, next = 1, ...otherFilters } = filters;

    return await this.request(
      `${this.BASE_URL}filters/${limit}/${next}`,
      {
        method: "GET",
        query: otherFilters,
        tag: `services-filtered-${limit}-${next}`,
      },
      ServicesListSchema,
    );
  }

  /**
   * Get random services
   */
  async getRandomServices(filters: Pick<ServiceFilters, 'extl_id' | 'categories'> = {}) {
    return await this.request(
      `${this.BASE_URL}random`,
      {
        method: "GET",
        query: filters,
        tag: "services-random",
      },
      z.array(ServiceSchema),
    );
  }

  /**
   * Get services by user external ID
   */
  async getServicesByUserId(userExtl_id: string) {
    return await this.request(
      `${this.BASE_URL}userExtl_id/${userExtl_id}`,
      {
        method: "GET",
        tag: `user-services-${userExtl_id}`,
      },
      z.array(ServiceSchema),
    );
  }
}

// Export schemas for reuse
export { ServiceSchema, ServicesListSchema, CategorySchema, ProviderSchema, PricingSchema };