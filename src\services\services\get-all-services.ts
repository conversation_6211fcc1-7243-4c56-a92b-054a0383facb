import { ServicesListSchema } from "@/schemas/services";
import { BaseApiService } from "../base-service";

export class GetAllServicesService extends BaseApiService {
  private BASE_URL = "services/";

  constructor() {
    super();
  }

  /**
   * Get all services with optional filters
   */
  async getServices(filters: ServiceApiParams = {}) {
    const { limit = 12, next = 1, ...otherFilters } = filters;

    return await this.request(
      `${this.BASE_URL}all/${limit}/${next}`,
      {
        method: "GET",
        query: otherFilters,
        tag: `services-${limit}-${next}`,
      },
      ServicesListSchema,
    );
  }

  /**
   * Get services with advanced filters
   */
  async getServicesWithFilters(filters: ServiceApiParams) {
    const { limit = 12, next = 1, ...otherFilters } = filters;

    return await this.request(
      `${this.BASE_URL}filters/${limit}/${next}`,
      {
        method: "GET",
        query: otherFilters,
        tag: `services-filtered-${limit}-${next}`,
      },
      ServicesListSchema,
    );
  }
}
