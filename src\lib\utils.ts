import { createLoader, parseAsInteger, parseAsString } from "nuqs/server";

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const globalQueryParams = {
  limit: parseAsInteger.withDefault(12),
  next: parseAsInteger.withDefault(1),
  extl_id: parseAsString,
};

// Helper function to convert parsed params to API filters
export function convertToServiceFilters(params: any): ServiceApiParams {
  return {
    limit: params.limit || 12,
    next: params.next || 1,
    extl_id: params.extl_id || undefined,
  };
}

export const getGlobalQueryParams = createLoader(globalQueryParams);
