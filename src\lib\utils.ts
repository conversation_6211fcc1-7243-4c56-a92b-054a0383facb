import {
  createLoader,
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
} from "nuqs/server";

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const globalQueryParams = {
  limit: parseAsInteger.withDefault(12),
  next: parseAsInteger.withDefault(1),
  maxPrice: parseAsInteger,
  minPrice: parseAsInteger,
  order: parseAsString,
  evaluation: parseAsString,
  extl_id: parseAsString,
  categories: parseAsArrayOf(parseAsString),
  locations: parseAsArrayOf(parseAsString),
};

// Helper function to convert parsed params to API filters
export function convertToServiceFilters(params: any): ServiceApiParams {
  return {
    limit: params.limit || 12,
    next: params.next || 1,
    maxPrice: params.maxPrice || undefined,
    minPrice: params.minPrice || undefined,
    order: params.order || undefined,
    extl_id: params.extl_id || undefined,
    categories: params.categories || undefined,
    locations: params.locations || undefined,
  };
}

export const getGlobalQueryParams = createLoader(globalQueryParams);
