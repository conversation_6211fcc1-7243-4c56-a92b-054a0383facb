import { createLoader, parseAsInteger, parseAsString } from "nuqs/server";

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const globalQueryParams = {
  limit: parseAsInteger.withDefault(12),
  next: parseAsInteger.withDefault(1),
  extl_id: parseAsString,
};

// Helper function to convert parsed params to API filters
export function convertToServiceFilters(params: any): ServiceApiParams {
  return {
    limit: params.limit || 12,
    next: params.next || 1,
    extl_id: params.extl_id || undefined,
  };
}

// Helper function to convert API params to query parameters
export function convertToQueryParams(
  filters: Omit<ServiceApiParams, "limit" | "next">,
): Record<string, string | number | boolean | string[]> {
  const queryParams: Record<string, string | number | boolean | string[]> = {};

  if (filters.extl_id !== undefined) queryParams.extl_id = filters.extl_id;

  return queryParams;
}

export const getGlobalQueryParams = createLoader(globalQueryParams);
