"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useQueryStates, parseAsInteger, parseAsString, parseAsArrayOf } from "nuqs";
import { ServicesService } from "@/services/services/services-service";
import {
  servicesQueryKeys,
  type ServicesQueryParams,
  type ServiceSearchParams,
  type AddToFavoritesMutation,
  type RemoveFromFavoritesMutation,
  type CreateServiceMutation,
  type UpdateServiceMutation,
  type ValidateServiceMutation,
  type DeleteServiceMutation,
  defaultServiceSearchParams,
} from "@/services/services/types";

// Initialize services service instance
const servicesService = new ServicesService();

// nuqs parsers for URL state management
const searchParamsConfig = {
  categories: parseAsArrayOf(parseAsString).withDefault(defaultServiceSearchParams.categories),
  locations: parseAsArrayOf(parseAsString).withDefault(defaultServiceSearchParams.locations),
  minPrice: parseAsInteger.withDefault(defaultServiceSearchParams.minPrice),
  maxPrice: parseAsInteger.withDefault(defaultServiceSearchParams.maxPrice),
  order: parseAsString.withDefault(defaultServiceSearchParams.order),
  page: parseAsInteger.withDefault(defaultServiceSearchParams.page),
  limit: parseAsInteger.withDefault(defaultServiceSearchParams.limit),
  search: parseAsString.withDefault(defaultServiceSearchParams.search),
} as const;

/**
 * Hook for managing services list with URL state synchronization
 */
export function useServices() {
  const [searchParams, setSearchParams] = useQueryStates(searchParamsConfig);

  // Convert search params to API filters
  const filters: ServicesQueryParams = {
    limit: searchParams.limit,
    next: searchParams.page,
    maxPrice: searchParams.maxPrice > 0 ? searchParams.maxPrice : undefined,
    minPrice: searchParams.minPrice > 0 ? searchParams.minPrice : undefined,
    order: searchParams.order as any,
    categories: searchParams.categories.length > 0 ? searchParams.categories : undefined,
    locations: searchParams.locations.length > 0 ? searchParams.locations : undefined,
  };

  const query = useQuery({
    queryKey: servicesQueryKeys.list(filters),
    queryFn: () => servicesService.getServicesWithFilters(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    ...query,
    searchParams,
    setSearchParams,
    filters,
    // Helper functions for updating specific params
    setCategories: (categories: string[]) => setSearchParams({ categories }),
    setLocations: (locations: string[]) => setSearchParams({ locations }),
    setPriceRange: (minPrice: number, maxPrice: number) => setSearchParams({ minPrice, maxPrice }),
    setOrder: (order: string) => setSearchParams({ order }),
    setPage: (page: number) => setSearchParams({ page }),
    setLimit: (limit: number) => setSearchParams({ limit }),
    setSearch: (search: string) => setSearchParams({ search }),
    resetFilters: () => setSearchParams(defaultServiceSearchParams),
  };
}

/**
 * Hook for getting a single service by ID
 */
export function useService(id: number, extl_id?: string) {
  return useQuery({
    queryKey: servicesQueryKeys.detail(id, extl_id),
    queryFn: () => servicesService.getServiceById(id, extl_id),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    enabled: !!id,
  });
}

/**
 * Hook for getting random services
 */
export function useRandomServices(filters?: Pick<ServicesQueryParams, 'extl_id' | 'categories'>) {
  return useQuery({
    queryKey: servicesQueryKeys.randomList(filters || {}),
    queryFn: () => servicesService.getRandomServices(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook for getting user's favorite services
 */
export function useFavoriteServices(limit: number = 12, page: number = 1) {
  return useQuery({
    queryKey: servicesQueryKeys.favoritesList(limit, page),
    queryFn: () => servicesService.getFavoriteServices(limit, page),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook for getting provider's services
 */
export function useProviderServices(limit: number = 12, page: number = 1) {
  return useQuery({
    queryKey: servicesQueryKeys.providerServices(limit, page),
    queryFn: () => servicesService.getProviderServices(limit, page),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook for getting services by user external ID
 */
export function useUserServices(userExtl_id: string) {
  return useQuery({
    queryKey: servicesQueryKeys.userServices(userExtl_id),
    queryFn: () => servicesService.getServicesByUserId(userExtl_id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!userExtl_id,
  });
}

/**
 * Hook for adding service to favorites
 */
export function useAddToFavorites() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ favorite }: AddToFavoritesMutation) =>
      servicesService.addToFavorites(favorite),
    onSuccess: () => {
      // Invalidate favorites queries
      queryClient.invalidateQueries({ queryKey: servicesQueryKeys.favorites() });
      // Invalidate services list to update isLiked status
      queryClient.invalidateQueries({ queryKey: servicesQueryKeys.lists() });
    },
  });
}

/**
 * Hook for removing service from favorites
 */
export function useRemoveFromFavorites() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ favorite }: RemoveFromFavoritesMutation) =>
      servicesService.removeFromFavorites(favorite),
    onSuccess: () => {
      // Invalidate favorites queries
      queryClient.invalidateQueries({ queryKey: servicesQueryKeys.favorites() });
      // Invalidate services list to update isLiked status
      queryClient.invalidateQueries({ queryKey: servicesQueryKeys.lists() });
    },
  });
}

/**
 * Hook for creating a new service
 */
export function useCreateService() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ serviceData, images }: CreateServiceMutation) =>
      servicesService.createService(serviceData, images),
    onSuccess: () => {
      // Invalidate provider services
      queryClient.invalidateQueries({ queryKey: servicesQueryKeys.provider() });
      // Invalidate services list
      queryClient.invalidateQueries({ queryKey: servicesQueryKeys.lists() });
    },
  });
}

/**
 * Hook for updating a service
 */
export function useUpdateService() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ serviceData, images }: UpdateServiceMutation) =>
      servicesService.updateService(serviceData, images),
    onSuccess: (data) => {
      // Invalidate specific service
      queryClient.invalidateQueries({ queryKey: servicesQueryKeys.detail(data.response.id) });
      // Invalidate provider services
      queryClient.invalidateQueries({ queryKey: servicesQueryKeys.provider() });
      // Invalidate services list
      queryClient.invalidateQueries({ queryKey: servicesQueryKeys.lists() });
    },
  });
}

/**
 * Hook for validating/publishing a service
 */
export function useValidateService() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ serviceId, isPublished }: ValidateServiceMutation) =>
      servicesService.validateService(serviceId, isPublished),
    onSuccess: (_, { serviceId }) => {
      // Invalidate specific service
      queryClient.invalidateQueries({ queryKey: servicesQueryKeys.detail(serviceId) });
      // Invalidate provider services
      queryClient.invalidateQueries({ queryKey: servicesQueryKeys.provider() });
    },
  });
}

/**
 * Hook for deleting a service
 */
export function useDeleteService() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id }: DeleteServiceMutation) =>
      servicesService.deleteService(id),
    onSuccess: (_, { id }) => {
      // Remove specific service from cache
      queryClient.removeQueries({ queryKey: servicesQueryKeys.detail(id) });
      // Invalidate provider services
      queryClient.invalidateQueries({ queryKey: servicesQueryKeys.provider() });
      // Invalidate services list
      queryClient.invalidateQueries({ queryKey: servicesQueryKeys.lists() });
    },
  });
}
