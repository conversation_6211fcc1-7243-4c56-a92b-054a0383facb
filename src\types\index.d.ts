declare type ApiResponse<T> = {
  status: number;
  response: T;
};

declare type ListApiResponse<T> = {
  nb_items: number;
  next_page: number | null;
  pagination: number;
  items: T[];
};

// Core Service Types
declare interface Service {
  id: number;
  name: string;
  description: string;
  longDescription: string;
  isPublished: boolean;
  isLiked: boolean | null;
  categories: Category[];
  provider: Provider;
  img: string[];
  pricing: Pricing;
  locations: string[];
  createdAt: string | null;
  updatedAt: string | null;
  status: ServiceStatus;
  rejectionReason: string | null;
}

declare interface Category {
  id: number;
  intl_id: string;
  name: string;
}

declare interface Provider {
  id: number;
  extl_id: string;
  note: number | null;
  status: ProviderStatus;
  firstName: string;
  lastName: string;
  imageUrl: string | null;
  email: string | null;
  unsafe_metadata: Record<string, any> | null;
}

declare interface Pricing {
  price: number;
  priceEuro: number | null;
  iso_curry: string;
  pricingModel: PricingModel;
}

// Enums and Union Types
declare type ServiceStatus = "active" | "inactive" | "deleted" | "pending";
declare type ProviderStatus = "active" | "inactive" | "deleted" | "locked";
declare type PricingModel = "fixed" | "hourly" | "daily" | "custom";
declare type SortOrder = "recent" | "old" | "desc_price" | "asc_price";

// User and Notification Types
declare interface User {
  id: number;
  extl_id: string;
  firstName: string;
  lastName: string;
  email: string;
  imageUrl: string | null;
  status: ProviderStatus;
  note: number | null;
  unsafe_metadata: UserMetadata | null;
  createdAt: string | null;
  updatedAt: string | null;
}

declare interface UserMetadata {
  title?: string;
  about_me?: string;
  currency?: string;
  language?: string;
  username?: string;
  phone_number?: string;
  address_email?: string;
  date_of_birth?: string;
  notifications?: string[];
  postal_address?: {
    city?: string;
    country?: string;
  };
  contact_address?: {
    phone_number?: string;
    address_email?: string;
    phone_number_country_code?: string;
  };
}

declare interface Notification {
  id: number;
  title: string;
  body: string;
  status: NotificationStatus;
  createdAt: string;
  updatedAt: string;
  userId: number;
}

declare type NotificationStatus = "read" | "unread" | "archived";

// Support and Service Request Types
declare interface Support {
  id: number;
  name: string;
  message: string;
  status: SupportStatus;
  extl_id: string;
  createdAt: string;
  updatedAt: string;
}

declare type SupportStatus = "open" | "in_progress" | "resolved" | "closed";

declare interface ServiceRequest {
  id: number;
  serviceId: number;
  clientExtl_Id: string;
  requestDetails: string;
  requestDate: string;
  duration: string;
  location: string;
  requestStatus: ServiceRequestStatus;
  quotation?: Quotation;
  createdAt: string;
  updatedAt: string;
}

declare type ServiceRequestStatus =
  | "PENDING"
  | "REJECTED"
  | "VALIDATED"
  | "CLOSED"
  | "CANCELLED"
  | "OPEN";

declare interface Quotation {
  quotationNumber: string;
  dateTime: string;
  durationEstimated: string;
  location: string;
  description: QuotationItem[];
  paymentInstructions: string | null;
}

declare interface QuotationItem {
  description: string;
  quantity: number;
  unitPrice: number;
}

// Rating Types
declare interface Rating {
  id: number;
  rating: number;
  comment: string;
  serviceId: number;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

// Favorite Types
declare interface Favorite {
  extl_id: string;
  service_id: number;
}

// API Request/Response Types for Services
declare interface ServiceFilters {
  limit?: number;
  next?: number;
  maxPrice?: number;
  minPrice?: number;
  order?: SortOrder;
  extl_id?: string;
  categories?: string[];
  locations?: string[];
}

// API Query Parameters (for internal service calls)
declare interface ServiceApiParams {
  limit?: number;
  next?: number;
  extl_id?: string;
}

declare interface CreateServiceData {
  name: string;
  description: string;
  longDescription?: string;
  categories: string[];
  pricing?: {
    price: number;
    iso_curry: string;
    pricingModel: PricingModel;
  };
  provider_id: string;
  locations?: string[];
  isPublished?: boolean;
}

declare interface UpdateServiceData extends Partial<CreateServiceData> {
  id: number;
  img?: string[];
  status?: ServiceStatus;
}

// Conversation Types
declare interface Conversation {
  id: number;
  providerId: string;
  serviceId: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}
