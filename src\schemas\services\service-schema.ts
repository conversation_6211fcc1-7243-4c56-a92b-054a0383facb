import { z } from "zod";
import { CategorySchema } from "./category-schema";
import { ProviderSchema } from "./provider-schema";
import { PricingSchema } from "./pricing-schema";

export const ServiceSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string(),
  longDescription: z.string(),
  isPublished: z.boolean(),
  isLiked: z.boolean().nullable(),
  categories: z.array(CategorySchema),
  provider: ProviderSchema,
  img: z.array(z.string()),
  pricing: PricingSchema,
  locations: z.array(z.string()),
  createdAt: z.string().nullable(),
  updatedAt: z.string().nullable(),
  status: z.enum(["active", "inactive", "deleted", "pending"]),
  rejectionReason: z.string().nullable(),
});

export type ServiceType = z.infer<typeof ServiceSchema>;
