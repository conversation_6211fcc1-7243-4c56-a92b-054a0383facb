import { z } from "zod";
import { BaseApiService } from "../base-service";
import { ServiceSchema } from "@/schemas/services";

export class GetServicesByUserIdService extends BaseApiService {
  private BASE_URL = "services/";

  constructor() {
    super();
  }

  /**
   * Get services by user external ID
   */
  async getServicesByUserId(userExtl_id: string) {
    return await this.request(
      `${this.BASE_URL}userExtl_id/${userExtl_id}`,
      {
        method: "GET",
        tag: `user-services-${userExtl_id}`,
      },
      z.array(ServiceSchema),
    );
  }
}
