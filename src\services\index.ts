// Base service
export * from "./base-service";

// Services
export * from "./services";

// Service types
export * from "./services/types";

// Service instances (singletons)
import { GetAllServicesService, GetServicesByUserIdService } from "./services";

export const getAllServicesService = new GetAllServicesService();
export const getServicesByUserIdService = new GetServicesByUserIdService();

// Legacy exports (to be removed)
export * from "./example-services";
