"use client";

import { useQuery } from "@tanstack/react-query";
import { GetServicesByUserIdService } from "@/services/services/get-services-by-user-id";

// Initialize service instance
const getServicesByUserIdService = new GetServicesByUserIdService();

/**
 * Hook for getting services by user external ID
 */
export function useServicesByUser(userExtl_id: string) {
  return useQuery({
    queryKey: ["services", "user", userExtl_id],
    queryFn: () => getServicesByUserIdService.getServicesByUserId(userExtl_id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!userExtl_id,
  });
}
