import { z } from "zod";

export const ProviderSchema = z.object({
  id: z.number(),
  extl_id: z.string(),
  note: z.number().nullable(),
  status: z.enum(["active", "inactive", "deleted", "locked"]),
  firstName: z.string(),
  lastName: z.string(),
  imageUrl: z.string().nullable(),
  email: z.string().nullable(),
  unsafe_metadata: z.record(z.any()).nullable(),
});

export type ProviderType = z.infer<typeof ProviderSchema>;
