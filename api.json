{"swaggerDoc": {"openapi": "3.0.0", "paths": {"/services/all/{limit}/{next}": {"get": {"operationId": "ServiceController_fetchServices", "summary": "Get all Services", "parameters": [{"name": "limit", "required": false, "in": "path", "description": "the maximum items to retrieve", "schema": {"example": 10, "type": "number"}}, {"name": "next", "required": false, "in": "path", "description": "the page to retrieve ", "schema": {"example": 1, "type": "number"}}, {"name": "extl_id", "required": false, "in": "query", "description": "External user id", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["services"]}}, "/services/filters/{limit}/{next}": {"get": {"operationId": "ServiceController_filterServices", "summary": "Get all Services with filters options", "parameters": [{"name": "limit", "required": true, "in": "path", "description": "the maximum items to retrieve", "schema": {"example": 10, "type": "number"}}, {"name": "next", "required": true, "in": "path", "description": "the page", "schema": {"example": 1, "type": "number"}}, {"name": "maxPrice", "required": false, "in": "query", "description": "Max price", "schema": {"type": "string"}}, {"name": "minPrice", "required": false, "in": "query", "description": "Min price", "schema": {"type": "string"}}, {"name": "order", "required": false, "in": "query", "description": "Order", "schema": {"enum": ["recent", "old", "desc_price", "asc_price"], "type": "string"}}, {"name": "extl_id", "required": false, "in": "query", "description": "External provider id", "schema": {"type": "string"}}, {"name": "categories", "required": false, "in": "query", "description": "List of categories", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "locations", "required": false, "in": "query", "description": "List of locations", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": ""}}, "tags": ["services"]}}, "/services/random": {"get": {"operationId": "ServiceController_randomServices", "summary": "Get random Services", "parameters": [{"name": "extl_id", "required": false, "in": "query", "description": "External provider id. Useful when a client wants to see only the services of a provider", "schema": {"type": "string"}}, {"name": "categories", "required": false, "in": "query", "description": "List of categories", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": ""}}, "tags": ["services"]}}, "/services/detail/{id}": {"get": {"operationId": "ServiceController_findBydId", "summary": "Get Service by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "extl_id", "required": false, "in": "query", "description": "External user id", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["services"]}}, "/services/favorites/{limit}/{next}": {"get": {"operationId": "ServiceController_listFavorites", "summary": "Get Service in favorite", "parameters": [{"name": "limit", "required": true, "in": "path", "description": "the maximum items to retrieve", "schema": {"example": 10, "type": "number"}}, {"name": "next", "required": true, "in": "path", "description": "the page", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["services"], "security": [{"bearer": []}]}}, "/services/favorite": {"post": {"operationId": "ServiceController_addFavorite", "summary": "Add Service to favorite", "parameters": [], "requestBody": {"required": true, "description": "Add Service to favorite", "content": {"application/json": {"schema": {"type": "object", "properties": {"favorite": {"type": "object", "properties": {"extl_id": {"type": "string", "example": "user_xxx"}, "service_id": {"type": "number", "example": 1}}, "required": ["extl_id", "service_id"]}}, "required": ["favorite"]}}}}, "responses": {"201": {"description": ""}}, "tags": ["services"], "security": [{"bearer": []}]}, "delete": {"operationId": "ServiceController_removeFavorite", "summary": "Remove Service from favorite", "parameters": [], "requestBody": {"required": true, "description": "Remove Service from favorite", "content": {"application/json": {"schema": {"type": "object", "properties": {"favorite": {"type": "object", "properties": {"extl_id": {"type": "string", "example": "user_xxx"}, "service_id": {"type": "number", "example": 1}}, "required": ["extl_id", "service_id"]}}, "required": ["favorite"]}}}}, "responses": {"200": {"description": ""}}, "tags": ["services"], "security": [{"bearer": []}]}}, "/services/providers/{limit}/{next}": {"get": {"operationId": "ServiceController_listProvidersServices", "summary": "Get Provider's Services. This is for the provider", "parameters": [{"name": "limit", "required": true, "in": "path", "description": "the maximum items to retrieve", "schema": {"example": 10, "type": "number"}}, {"name": "next", "required": true, "in": "path", "description": "the page", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["services"], "security": [{"bearer": []}]}}, "/services/create": {"post": {"operationId": "ServiceController_create", "summary": "Create service", "parameters": [], "requestBody": {"required": true, "description": "Upload a set of images and a service object", "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"images": {"type": "array", "items": {"type": "string", "format": "binary"}, "description": "Array of image files"}, "service": {"type": "object", "properties": {"name": {"type": "string", "example": "Animation DJ"}, "description": {"type": "string", "example": "Events animation"}, "longDescription": {"type": "string", "example": "A professional DJ who makes all your guests happy..."}, "categories": {"type": "array", "items": {"type": "string", "example": "anim"}, "example": ["anim", "photo_video"], "description": "List of categories related to the service"}, "pricing": {"type": "object", "properties": {"price": {"type": "number", "format": "float", "example": "15.99"}, "iso_curry": {"type": "string", "example": "eur", "maxLength": 3, "description": "3 chars ISO curry"}, "pricingModel": {"type": "string", "example": "fixed", "description": "Pricing model (e.g., fixed, hourly)"}}, "description": "The pricing details of the service"}, "provider_id": {"type": "string", "example": "extl_1"}, "locations": {"type": "array", "items": {"type": "string", "example": "CMR"}, "example": ["CMR", "FRA"], "description": "List of countries ISO code where the service is available"}, "isPublished": {"type": "boolean", "example": "true"}}, "required": ["name", "description", "categories"]}}, "required": ["service"]}}}}, "responses": {"201": {"description": ""}}, "tags": ["services"], "security": [{"bearer": []}]}}, "/services/edit": {"put": {"operationId": "ServiceController_edit", "summary": "Edit service", "parameters": [], "requestBody": {"required": true, "description": "Upload a set of images and a service object", "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"images": {"type": "array", "items": {"type": "string", "format": "binary"}, "description": "Array of image files"}, "service": {"type": "object", "properties": {"id": {"type": "number", "example": 1, "description": "id of the updated service"}, "name": {"type": "string", "example": "Animation DJ"}, "description": {"type": "string", "example": "Events animation"}, "longDescription": {"type": "string", "example": "A professional DJ who makes all your guests happy..."}, "categories": {"type": "array", "items": {"type": "string", "example": "anim"}, "example": ["anim", "restaurant"], "description": "List of categories related to the service"}, "pricing": {"type": "object", "properties": {"price": {"type": "number", "format": "float", "example": "15.99"}, "iso_curry": {"type": "string", "example": "eur", "maxLength": 3, "description": "3 chars ISO curry"}, "pricingModel": {"type": "string", "example": "fixed"}}, "description": "The pricing details of the service"}, "provider_id": {"type": "string", "example": "extl_1"}, "locations": {"type": "string", "items": {"type": "string", "example": "CMR"}, "example": ["CMR"], "description": "List of countries ISO code where the service is available"}, "img": {"type": "string", "items": {"type": "string", "example": "link 1"}, "example": ["link 1"], "description": "link of images remote repos"}, "isPublished": {"type": "boolean", "example": "true"}, "status": {"type": "string", "example": "active"}}, "required": ["name", "description", "categories"]}}, "required": ["service"]}}}}, "responses": {"200": {"description": ""}}, "tags": ["services"], "security": [{"bearer": []}]}}, "/services/validate/{serviceId}/{isPublished}": {"put": {"operationId": "ServiceController_validate", "summary": "Change the isPublished value", "parameters": [{"name": "serviceId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "isPublished", "required": true, "in": "path", "schema": {"type": "boolean"}}], "responses": {"200": {"description": ""}}, "tags": ["services"], "security": [{"bearer": []}]}}, "/services/userExtl_id/{userExtl_id}": {"get": {"operationId": "ServiceController_findBydUserId", "summary": "Get all given User's Services by ID", "parameters": [{"name": "userExtl_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["services"]}}, "/services/delete/{id}": {"delete": {"operationId": "ServiceController_deleteService", "summary": "Delete service by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["services"], "security": [{"bearer": []}]}}, "/notifications/user/{limit}/{next}": {"get": {"operationId": "NotificationController_fetchUserNotifications", "summary": "Get User Notifications. It need the Authorization to get User Extl_id", "parameters": [{"name": "limit", "required": true, "in": "path", "description": "the maximum items to retrieve", "schema": {"example": 10, "type": "number"}}, {"name": "next", "required": true, "in": "path", "description": "the page to retrieve ", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["notifications"], "security": [{"bearer": []}]}}, "/notifications/user/test/{limit}/{next}": {"get": {"operationId": "NotificationController_fetchUserNotificationsTest", "summary": "Get User Notifications. It need the Authorization to get User Extl_id", "parameters": [{"name": "limit", "required": true, "in": "path", "description": "the maximum items to retrieve", "schema": {"example": 10, "type": "number"}}, {"name": "next", "required": true, "in": "path", "description": "the page to retrieve ", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["notifications"]}}, "/notifications/id/{id}": {"get": {"operationId": "NotificationController_findBydId", "summary": "Get Notification by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["notifications"], "security": [{"bearer": []}]}, "delete": {"operationId": "NotificationController_deleteById", "summary": "Delete Notification by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["notifications"], "security": [{"bearer": []}]}}, "/notifications/read/{id}/{newstatus}": {"put": {"operationId": "NotificationController_edit", "summary": "Edit notification", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "newstatus", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["notifications"], "security": [{"bearer": []}]}}, "/notifications/subscribe": {"post": {"operationId": "NotificationController_subscribe", "summary": "Subscribe to push notification", "parameters": [], "requestBody": {"required": true, "description": "Save a device token of the user", "content": {"application/json": {"schema": {"type": "object", "properties": {"subscription": {"type": "object", "properties": {"device": {"type": "string", "example": "web"}, "token": {"type": "string", "example": "xxxxxxxxxxxxx"}}, "required": ["token"]}, "extl_id": {"type": "string", "example": "User_xxx"}}, "required": ["subscription"]}}}}, "responses": {"201": {"description": ""}}, "tags": ["notifications"]}}, "/notifications": {"post": {"operationId": "NotificationController_pushNotification", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendNotificationDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["notifications"]}}, "/users/all/{limit}/{next}": {"get": {"operationId": "UserController_fetchUsers", "summary": "Get all Users", "parameters": [{"name": "limit", "required": true, "in": "path", "description": "the maximum items to retrieve", "schema": {"example": 10, "type": "number"}}, {"name": "next", "required": true, "in": "path", "description": "the page to retrieve ", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/admins": {"get": {"operationId": "UserController_fetchAdmins", "summary": "Get all Admins", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/extl_id/{extl_id}": {"get": {"operationId": "UserController_findBydId", "summary": "Get User by external ID", "parameters": [{"name": "extl_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/clerk": {"post": {"operationId": "UserController_handleClerkWebhook", "summary": "user webhook create user", "parameters": [{"name": "svix-signature", "required": true, "in": "header", "schema": {"type": "string"}}, {"name": "svix-timestamp", "required": true, "in": "header", "schema": {"type": "string"}}, {"name": "svix-id", "required": true, "in": "header", "schema": {"type": "string"}}], "requestBody": {"required": true, "description": "create user from clerk webhook", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/"}}}}, "responses": {"201": {"description": ""}}, "tags": ["users"]}}, "/users/delete/{extl_id}": {"delete": {"operationId": "UserController_delete", "summary": "Delete user (set status to DELETED)", "parameters": [{"name": "extl_id", "required": true, "in": "path", "description": "External user ID", "schema": {"example": "user_extl_id", "type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/users/deactivate/{extl_id}": {"patch": {"operationId": "UserController_deactivate", "summary": "Deactivate user (set status to LOCKED)", "parameters": [{"name": "extl_id", "required": true, "in": "path", "description": "External user ID", "schema": {"example": "user_extl_id", "type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["users"]}}, "/rating": {"post": {"operationId": "RatingController_create", "summary": "Create a new rating", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRatingDTO"}}}}, "responses": {"201": {"description": ""}}, "tags": ["ratings"], "security": [{"bearer": []}]}}, "/categories": {"get": {"operationId": "CategoryController_fetchServices", "summary": "Get all Categorie", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["categories"]}}, "/categories/intl_id/{intl_id}": {"get": {"operationId": "CategoryController_findByIntl_id", "summary": "Get Category by internal Id (intl_id)", "parameters": [{"name": "intl_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["categories"]}}, "/supports/all/{limit}/{next}": {"get": {"operationId": "SupportController_fetchSupports", "summary": "Get all Supports", "parameters": [{"name": "limit", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "next", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["supports"]}}, "/supports/id/{id}": {"get": {"operationId": "SupportController_findBydId", "summary": "Get Support by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["supports"]}}, "/supports/create": {"post": {"operationId": "SupportController_create", "summary": "Create support", "parameters": [], "requestBody": {"required": true, "description": "Create a support object", "content": {"application/json": {"schema": {"type": "object", "properties": {"support": {"type": "object", "properties": {"name": {"type": "string", "example": "Name"}, "message": {"type": "string", "example": "Support message"}}, "required": ["message"]}, "extl_id": {"type": "string", "example": "User_xxx"}}, "required": ["support"]}}}}, "responses": {"201": {"description": ""}}, "tags": ["supports"]}}, "/supports/edit": {"put": {"operationId": "SupportController_edit", "summary": "Edit support", "parameters": [], "requestBody": {"required": true, "description": "Edit support object", "content": {"application/json": {"schema": {"type": "object", "properties": {"support": {"type": "object", "properties": {"id": {"type": "number", "example": 1, "description": "id of the updated support"}, "name": {"type": "string", "example": "Name"}, "message": {"type": "string", "example": "Support message"}}, "required": ["name", "message"]}}, "required": ["support"]}}}}, "responses": {"200": {"description": ""}}, "tags": ["supports"]}}, "/serviceRequests": {"get": {"operationId": "ServiceRequestController_fetchServiceRequests", "summary": "Get all ServiceRequests", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["serviceRequests"]}}, "/serviceRequests/id/{id}": {"get": {"operationId": "ServiceRequestController_findBydId", "summary": "Get Service Request by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["serviceRequests"]}}, "/serviceRequests/create": {"post": {"operationId": "ServiceRequestController_create", "summary": "Create service Request for a service: does not contain any quotation, default status OPEN", "parameters": [], "requestBody": {"required": true, "description": "Upload a set of images and a service request object", "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"images": {"type": "array", "items": {"type": "string", "format": "binary"}, "description": "Array of image files"}, "serviceRequest": {"type": "object", "properties": {"serviceId": {"type": "number", "description": "Requested Service ID", "example": 1}, "clientExtl_Id": {"type": "string", "description": "External ID of the client requesting the service", "example": "extl_1"}, "requestDetails": {"type": "string", "description": "Details of the service request", "example": "Request for plumbing service.", "maxLength": 300}, "requestDate": {"type": "string", "format": "date", "description": "Date of the service request", "example": "2024-09-21"}, "duration": {"type": "string", "description": "Duration of the service request", "example": "02:30:00"}, "location": {"type": "string", "description": "Location of the service request", "example": "123 Main St, Cityville"}, "requestStatus": {"type": "string", "description": "Current status of the request", "enum": ["PENDING", "REJECTED", "VALIDATED", "CLOSED", "CANCELLED", "OPEN"], "example": "PENDING"}}, "required": ["serviceId", "clientExtl_Id", "requestDetails", "duration", "location"]}}}}}}, "responses": {"201": {"description": ""}}, "tags": ["serviceRequests"], "security": [{"bearer": []}]}}, "/serviceRequests/create/quotation": {"put": {"operationId": "ServiceRequestController_createServiceRequestQuotation", "summary": "update service request: create/update quotation", "parameters": [], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"quotation": {"type": "object", "properties": {"quotationNumber": {"type": "string", "readOnly": true, "description": "Unique ID of the quotation", "example": "QTN123456"}, "dateTime": {"type": "string", "format": "date-time", "description": "Date and time of the quotation in ISO format", "example": "2024-09-21"}, "durationEstimated": {"type": "string", "description": "Estimated duration for the quotation (e.g., in hours)", "example": "04:00:00"}, "location": {"type": "string", "description": "Location associated with the quotation", "example": "Yaounde, Cameroon", "nullable": true}, "description": {"type": "array", "items": {"type": "object", "properties": {"description": {"type": "string", "description": "Description of the item or service", "example": "Web development service"}, "quantity": {"type": "number", "description": "Quantity of the service or item", "example": 2}, "unitPrice": {"type": "number", "description": "Unit price of the service or item", "example": 1000}}}, "description": "List of items or services in the quotation", "example": [{"description": "Web development service", "quantity": 2, "unitPrice": 1000}, {"description": "SEO optimization service", "quantity": 1, "unitPrice": 500}]}, "paymentInstructions": {"type": "string", "description": "Payment instructions for the quotation", "example": "50% upfront, balance upon completion", "nullable": true}}, "required": ["dateTime", "location"]}, "serviceRequest": {"description": "Service request for the quotation", "type": "object", "properties": {"serviceRequestId": {"type": "number", "description": "Service Request ID", "example": 1}}}}}}}}, "responses": {"200": {"description": ""}}, "tags": ["serviceRequests"], "security": [{"bearer": []}]}}, "/userServiceRequests": {"get": {"operationId": "UserServiceRequestController_fetchUserServiceRequests", "summary": "Get all UserServiceRequests", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["userServiceRequests"]}}, "/userServiceRequests/all/extl_id/{extl_id}": {"get": {"operationId": "UserServiceRequestController_findBydExtl_Id", "summary": "Get UserServiceRequest by user external ID", "parameters": [{"name": "extl_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["userServiceRequests"]}}, "/userServiceRequests/serviceRequestId/{serviceRequestId}": {"get": {"operationId": "UserServiceRequestController_findByServiceRequestId", "summary": "Get User Service Request by service Request ID", "parameters": [{"name": "serviceRequestId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["userServiceRequests"]}}, "/conversations/start": {"post": {"operationId": "ConversationController_createConversation", "parameters": [{"name": "providerId", "required": true, "in": "query", "description": "ID of the provider", "schema": {"type": "string"}}, {"name": "serviceId", "required": true, "in": "query", "description": "ID of the service", "schema": {"type": "string"}}, {"name": "userId", "required": true, "in": "query", "description": "ID of the user", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["conversations"]}}}, "info": {"title": "Afreesrv-backend", "description": "AFREESERV BACK-END API", "version": "1.0", "contact": {}}, "tags": [], "servers": [{"url": "https://api-dev.afreeserv.com/", "description": "Dev environment"}, {"url": "", "description": ""}], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"SendNotificationDto": {"type": "object", "properties": {"title": {"type": "string"}, "body": {"type": "string"}, "deviceId": {"type": "string"}}, "required": ["title", "body", "deviceId"]}, "UserRatingDTO": {"type": "object", "properties": {"raterId": {"type": "number", "example": 1, "description": "ID of the user giving the rating"}, "ratedUserId": {"type": "number", "example": 2, "description": "ID of the user being rated"}, "rating": {"type": "number", "example": 5, "description": "Rating value from 1 to 5", "maximum": 5, "minimum": 1}, "comment": {"type": "string", "example": "Great job!", "description": "Optional comment about the rating"}}, "required": ["raterId", "ratedUserId", "rating", "comment"]}}}}, "customOptions": {}}