"use client";

import { useServicesList, useServicesByUser } from "@/hooks/services";

export function ServicesExample() {
  // Hook for services list with URL state management
  const {
    data: servicesList,
    isLoading: isServicesLoading,
    searchParams,
    setPage,
    setLimit,
    resetFilters,
  } = useServicesList();

  // Hook for user-specific services
  const {
    data: userServices,
    isLoading: isUserServicesLoading,
  } = useServicesByUser("user123");

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Services Architecture Example</h2>
      
      {/* Services List Section */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-4">All Services</h3>
        
        {/* Current filters display */}
        <div className="mb-4 text-sm text-gray-600">
          <p>Current page: {searchParams.page}</p>
          <p>Limit: {searchParams.limit}</p>
        </div>
        
        {/* Controls */}
        <div className="flex gap-2 mb-4">
          <button
            onClick={() => setPage(searchParams.page + 1)}
            className="px-3 py-1 bg-blue-500 text-white rounded"
            disabled={isServicesLoading}
          >
            Next Page
          </button>
          <button
            onClick={() => setLimit(6)}
            className="px-3 py-1 bg-green-500 text-white rounded"
          >
            Show 6
          </button>
          <button
            onClick={() => setLimit(12)}
            className="px-3 py-1 bg-green-500 text-white rounded"
          >
            Show 12
          </button>
          <button
            onClick={resetFilters}
            className="px-3 py-1 bg-red-500 text-white rounded"
          >
            Reset
          </button>
        </div>
        
        {/* Services display */}
        {isServicesLoading ? (
          <p>Loading services...</p>
        ) : servicesList?.response ? (
          <div>
            <p className="mb-2">
              Found {servicesList.response.nb_items} services
            </p>
            <div className="grid gap-2">
              {servicesList.response.items.slice(0, 3).map((service) => (
                <div key={service.id} className="border p-2 rounded">
                  <h4 className="font-medium">{service.name}</h4>
                  <p className="text-sm text-gray-600">{service.description}</p>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <p>No services found</p>
        )}
      </div>
      
      {/* User Services Section */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-4">User Services</h3>
        
        {isUserServicesLoading ? (
          <p>Loading user services...</p>
        ) : userServices?.response ? (
          <div className="grid gap-2">
            {userServices.response.slice(0, 2).map((service) => (
              <div key={service.id} className="border p-2 rounded">
                <h4 className="font-medium">{service.name}</h4>
                <p className="text-sm text-gray-600">{service.description}</p>
              </div>
            ))}
          </div>
        ) : (
          <p>No user services found</p>
        )}
      </div>
    </div>
  );
}
