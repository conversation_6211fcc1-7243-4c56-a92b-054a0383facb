"use client";

import { useServicesList } from "@/hooks/services";
import { ServiceCard } from "./service-card";
import { ServicesFilters } from "./services-filters";

interface ServicesListClientProps {
  initialData: ListApiResponse<Service>;
  initialFilters: ServiceFilters;
}

export function ServicesListClient({ 
  initialData, 
  initialFilters 
}: ServicesListClientProps) {
  const {
    data,
    isLoading,
    isError,
    error,
    searchParams,
    setCategories,
    setLocations,
    setPriceRange,
    setOrder,
    setPage,
    resetFilters,
  } = useServicesList();

  // Use initial data if query data is not available yet
  const services = data?.response || initialData;

  if (isError) {
    return (
      <div className="text-red-500">
        Error loading services: {error?.message}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <ServicesFilters
        categories={searchParams.categories}
        locations={searchParams.locations}
        minPrice={searchParams.minPrice}
        maxPrice={searchParams.maxPrice}
        order={searchParams.order}
        onCategoriesChange={setCategories}
        onLocationsChange={setLocations}
        onPriceRangeChange={setPriceRange}
        onOrderChange={setOrder}
        onReset={resetFilters}
      />

      {/* Loading state */}
      {isLoading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading services...</p>
        </div>
      )}

      {/* Services grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {services.items.map((service) => (
          <ServiceCard key={service.id} service={service} />
        ))}
      </div>

      {/* Pagination */}
      {services.next_page && (
        <div className="flex justify-center">
          <button
            onClick={() => setPage(searchParams.page + 1)}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            disabled={isLoading}
          >
            Load More
          </button>
        </div>
      )}

      {/* Results info */}
      <div className="text-center text-muted-foreground">
        Showing {services.items.length} of {services.nb_items} services
      </div>
    </div>
  );
}
