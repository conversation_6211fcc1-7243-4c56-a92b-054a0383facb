"use client";

import { GetAllServicesService } from "@/services/services/get-all-services";
import { useQuery } from "@tanstack/react-query";
import {
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
  useQueryStates,
} from "nuqs";

// Initialize service instance
const getAllServicesService = new GetAllServicesService();

// Default search params
const defaultServiceSearchParams = {
  categories: [],
  locations: [],
  minPrice: 0,
  maxPrice: 10000,
  order: "recent" as const,
  page: 1,
  limit: 12,
  search: "",
};

// nuqs parsers for URL state management
const searchParamsConfig = {
  categories: parseAsArrayOf(parseAsString).withDefault(
    defaultServiceSearchParams.categories,
  ),
  locations: parseAsArrayOf(parseAsString).withDefault(
    defaultServiceSearchParams.locations,
  ),
  minPrice: parseAsInteger.withDefault(defaultServiceSearchParams.minPrice),
  maxPrice: parseAsInteger.withDefault(defaultServiceSearchParams.maxPrice),
  order: parseAsString.withDefault(defaultServiceSearchParams.order),
  page: parseAsInteger.withDefault(defaultServiceSearchParams.page),
  limit: parseAsInteger.withDefault(defaultServiceSearchParams.limit),
  search: parseAsString.withDefault(defaultServiceSearchParams.search),
} as const;

/**
 * Hook for managing services list with URL state synchronization
 */
export function useServicesList() {
  const [searchParams, setSearchParams] = useQueryStates(searchParamsConfig);

  // Convert search params to API filters
  const filters: ServiceApiParams = {
    limit: searchParams.limit,
    next: searchParams.page,
  };

  const query = useQuery({
    queryKey: ["services", "list", filters],
    queryFn: () => getAllServicesService.getWithFilters(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    ...query,
    searchParams,
    setSearchParams,
    filters,
    // Helper functions for updating specific params
    setCategories: (categories: string[]) => setSearchParams({ categories }),
    setLocations: (locations: string[]) => setSearchParams({ locations }),
    setPriceRange: (minPrice: number, maxPrice: number) =>
      setSearchParams({ minPrice, maxPrice }),
    setOrder: (order: string) => setSearchParams({ order }),
    setPage: (page: number) => setSearchParams({ page }),
    setLimit: (limit: number) => setSearchParams({ limit }),
    setSearch: (search: string) => setSearchParams({ search }),
    resetFilters: () => setSearchParams(defaultServiceSearchParams),
  };
}
