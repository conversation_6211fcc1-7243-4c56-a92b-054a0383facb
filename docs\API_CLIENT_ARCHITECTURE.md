# API Client Architecture Guide

## Overview

Cette architecture API client décomposée respecte la contrainte de 100-130 lignes par fichier et suit les principes Clean Architecture avec NextJS 15+, TypeScript, nuqs et TanStack Query.

## Structure des Types

### Types Globaux (`src/types/index.d.ts`)
- `ServiceFilters` : Interface pour les filtres utilisateur
- `ServiceApiParams` : Interface pour les paramètres API internes
- Tous les types sont globaux (syntaxe `declare`)

### Conversion des Types
```typescript
// Utilise la fonction helper pour convertir les paramètres
import { convertToServiceFilters } from "@/lib/utils";

const apiParams = convertToServiceFilters(nuqsParams);
```

## Services API Décomposés

### Structure
```
src/services/services/
├── get-all-services.ts          # Services listing (44 lignes)
├── get-services-by-user-id.ts   # User services (25 lignes)
├── types.ts                     # Types spécifiques
└── index.ts                     # Exports (6 lignes)
```

### Utilisation
```typescript
import { GetAllServicesService } from "@/services/services";

const service = new GetAllServicesService();
const result = await service.getServicesWithFilters(filters);
```

## Schémas Zod Modulaires

### Structure
```
src/schemas/services/
├── category-schema.ts      # 8 lignes
├── provider-schema.ts      # 13 lignes
├── pricing-schema.ts       # 8 lignes
├── service-schema.ts       # 22 lignes
├── services-list-schema.ts # 10 lignes
└── index.ts               # 5 lignes
```

### Utilisation
```typescript
import { ServiceSchema, ServicesListSchema } from "@/schemas/services";
```

## Server Actions

### Fichier : `src/server/actions/services-actions.ts` (60 lignes)

```typescript
import { getServicesAction } from "@/server/actions/services-actions";

// Dans un Server Component
const result = await getServicesAction(filters);
```

## Hooks Décomposés

### Structure
```
src/hooks/services/
├── use-services-list.ts     # 68 lignes - Liste avec URL state
├── use-services-by-user.ts  # 18 lignes - Services par utilisateur
└── index.ts                # 3 lignes
```

### Utilisation
```typescript
import { useServicesList, useServicesByUser } from "@/hooks/services";

// Hook avec synchronisation URL
const {
  data,
  searchParams,
  setCategories,
  setPage,
  resetFilters
} = useServicesList();

// Hook simple
const { data: userServices } = useServicesByUser(userId);
```

## Flow de Données Complet

### 1. Server Component (SSR)
```typescript
// app/[locale]/services/page.tsx
export default async function ServicesPage({ searchParams }) {
  const params = await getGlobalQueryParams(searchParams);
  const filters = convertToServiceFilters(params);
  const result = await getServicesAction(filters);
  
  return <ServicesListClient initialData={result.data} />;
}
```

### 2. Client Component (Hydration + Updates)
```typescript
// components/features/services/services-list-client.tsx
export function ServicesListClient({ initialData }) {
  const { data, setCategories, setPage } = useServicesList();
  const services = data?.response || initialData;
  
  return (
    <div>
      <Filters onCategoriesChange={setCategories} />
      <ServicesList services={services.items} />
    </div>
  );
}
```

## Avantages de cette Architecture

### ✅ Modularité
- Chaque fichier < 70 lignes
- Responsabilité unique par module
- Testabilité indépendante

### ✅ Type Safety
- Types globaux TypeScript
- Validation Zod automatique
- Conversion de types sécurisée

### ✅ Performance
- Cache intelligent TanStack Query
- Server-side rendering
- URL state synchronization

### ✅ Developer Experience
- Auto-completion complète
- Hot reload rapide
- Debugging facilité

## Patterns Utilisés

1. **Repository Pattern** : Services API comme repositories
2. **Observer Pattern** : TanStack Query pour la réactivité
3. **Factory Pattern** : Création de query keys dynamiques
4. **Adapter Pattern** : Conversion entre types nuqs et API

## Prochaines Étapes

1. **Tests** : Créer des tests unitaires pour chaque service
2. **Error Boundaries** : Ajouter la gestion d'erreurs React
3. **Optimistic Updates** : Mutations optimistes
4. **Cache Strategies** : Stratégies de cache granulaires
